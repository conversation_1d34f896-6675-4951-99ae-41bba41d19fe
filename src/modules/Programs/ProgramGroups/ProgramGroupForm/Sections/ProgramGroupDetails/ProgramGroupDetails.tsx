import classnames from 'classnames';
import React, { useMemo, useCallback } from 'react';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { useQuery } from 'react-apollo';
import { isNumber } from 'lodash';

import { BoxSizeWrapper } from '../../../../../../common/components/other/Box';

import styles from './ProgramGroupDetails.scss';

import EntityForm from '../../../../../../common/components/containers/EntityForm';
import StatusField from '../../../../../../common/components/containers/EntityForm/fields/StatusField';
import Statuses from '../../../../../../model/Statuses';

import EntityNameField from '../../../../../../common/components/containers/EntityForm/fields/EntityNameField';
import EntityFormFieldSet from '../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import handleDummyAction from '../../../../../../common/components/utils/Dummy/handleDummyAction';
import Box from '../../../../../../common/components/other/Box/Box';
import useT from '../../../../../../common/components/utils/Translations/useT';
import IProgramGroup from '../../../../../../common/abstract/OrganisationGroup/ProgramGroups/IProgramGroup';
import { IProgramGroupSection } from '../../ProgramGroupForm';
import ProgramCalendarSelectorField from '../../../../../../common/components/containers/EntityForm/fields/ProgramCalendarSelectorField';
import DefaultNumberOfCyclesForProgramSelectorField from '../../../../../../common/components/containers/EntityForm/fields/DefaultNumberOfCyclesForProgramSelectorField';

import programCalendarSelector from '../../../../../../common/data/programCalendarSelector.graphql';
import DependsOnField from '../../../../../../common/components/containers/EntityForm/DependsOnField';
import { resolveDefaultCyclesNumFromCalendar } from './helpers';

export interface IProgramGroupDetails {
  entity: Partial<IProgramGroup>;
  onGoBack: () => void;
  onSubmit: (values: IProgramGroup) => void;
  programGroupTypeId: number;
  orgGroupId: number;
  sections: IProgramGroupSection[];
}

const ProgramGroupDetails: React.FC<Readonly<IProgramGroupDetails>> = ({
  entity,
  onGoBack,
  onSubmit,
  programGroupTypeId,
  orgGroupId,
  sections,
}) => {
  const t = useT();

  const { url } = useRouteMatch();
  const history = useHistory();

  const { data, loading: isCalendarsLoading } = useQuery(
    programCalendarSelector,
    {
      variables: { orgGroupId },
      skip: !isNumber(orgGroupId),
    },
  );

  const resolveDefaultCyclesNum = useCallback(
    resolveDefaultCyclesNumFromCalendar(data?.programCalendarSelector),
    [data],
  );

  const defaultProgramCalendarId = useMemo(
    () =>
      data?.programCalendarSelector && data?.programCalendarSelector.length
        ? data?.programCalendarSelector[0].id
        : null,
    [data],
  );

  const initialValues = useMemo(
    () => ({
      orgGroupId,
      programGroupTypeId,
      programCalendarId: defaultProgramCalendarId,
      ...entity,
    }),
    [orgGroupId, entity, programGroupTypeId, defaultProgramCalendarId],
  );

  const isNew = useMemo(() => !entity?.id, [entity]);

  const handleSubmit = useCallback(entity => onSubmit(entity), [onSubmit]);

  const renderCard = useCallback(
    ({ path, title, cardBody }) => {
      const onClick = () => {
        if (isNew) {
          handleDummyAction();
        } else {
          history.push(`${url}/${path}`);
        }
      };

      return (
        <Box
          key={`program-group-${path}`}
          href={`${url}/${path}`}
          title={title}
          onClick={onClick}
        >
          {cardBody}
        </Box>
      );
    },
    [url, history, isNew],
  );

  const sectionsClassName = classnames('grid-view', {
    [styles.disabledSections]: isNew,
  });

  return (
    <EntityForm
      entity={initialValues}
      entityName={`program-group-details-form-${isNew ? 'new' : entity.id}`}
      onGoBack={onGoBack}
      onSubmit={handleSubmit}
    >
      <EntityFormFieldSet className="mb-20">
        <EntityNameField columns={4} label={t('Program Group Name')} />
        <ProgramCalendarSelectorField
          columns={4}
          loading={isCalendarsLoading}
          name="programCalendarId"
          options={data?.programCalendarSelector || []}
          orgGroupId={orgGroupId}
        />

        <DependsOnField<number> fieldName="programCalendarId">
          {programCalendarId => (
            <DefaultNumberOfCyclesForProgramSelectorField
              calendar={resolveDefaultCyclesNum(programCalendarId)}
              columns={4}
            />
          )}
        </DependsOnField>

        <StatusField
          columns={4}
          defaultValue={Statuses.Active.value}
          isNew={isNew}
          label={t('Status')}
        />
      </EntityFormFieldSet>
      <EntityFormFieldSet className={sectionsClassName}>
        <BoxSizeWrapper>{sections.map(renderCard)}</BoxSizeWrapper>
      </EntityFormFieldSet>
    </EntityForm>
  );
};

export default ProgramGroupDetails;
