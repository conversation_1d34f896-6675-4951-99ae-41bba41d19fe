import { filter } from 'lodash';
import React, { FC } from 'react';
import GqlStaticTreeSelector from '../../../../common/components/controls/FilterBar/GqlStaticTreeSelector';
import useT from '../../../../common/components/utils/Translations/useT';
import useCurrentUser from '../../../../common/data/hooks/useCurrentUser';
import query from '../../../../modules/Organization/OrganisationGroups/EdComSetup/channels/EmailChannel/data/emailCategoriesTree.graphql';
import {
  isCategory,
  resolveNodeId,
  resolveNodeParentId,
  canRender,
} from '../../../Organization/OrganisationGroups/EdComSetup/channels/EmailChannel/model/EmailChannelTypes';

import { TFilterComponent } from '../../EdComCrud/context/EdComFilterBar';
import IEmailChannelCategory from '../../../../common/abstract/EdCom/Emails/IEmailChannelCategory';
import { isRootNode } from '../../../../common/components/dataViews/DynamicTree';
import Button from '../../../../common/components/controls/Button';
import StatusAddon from '../../../../common/components/dataViews/NextTree/addons/StatusAddon';

const EmailCategoriesFilter: FC<TFilterComponent<IEmailChannelCategory[]>> = ({
  onChange,
  value,
}) => {
  const t = useT();
  const {
    me: { organisationGroupId },
  } = useCurrentUser();

  const renderNodeAddons = (node, meta) => {
    const { id } = node;
    const { handleClear } = meta;

    const handleClearFn = e => {
      handleClear();
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
    };

    const addons: JSX.Element[] = [];
    if (isRootNode(node)) {
      addons.push(
        <Button
          additionClasses="text-primary no-padding-top no-padding-bottom"
          buttonStyle="link"
          onClick={handleClearFn}
        >
          {t('Clear')}
        </Button>,
      );
    }

    if (!isRootNode(node)) {
      addons.push(<StatusAddon key={`status_${id}`} status={node.status} />);
    }

    return addons;
  };

  return (
    <GqlStaticTreeSelector
      hasFilter
      hasSyntheticRootNode
      initialSelectAll
      adapter={{
        renderNodeAddons,
        resolveNodeId,
        resolveNodeParentId,
        nodeIsLeaf: isCategory,
        canRender,
      }}
      gql={query}
      gqlSkip={!organisationGroupId}
      gqlVariables={{
        orgGroupId: organisationGroupId,
        isSelector: true,
      }}
      hasClearButton={false}
      hasMarginLeft={false}
      isSingleSelector={false}
      name="emailChannelCategories"
      selectionFilter={selectionFilter}
      syntheticRootNodeName={t('Email Categories')}
      title={t('Category')}
      value={value}
      onChange={onChange}
    />
  );
};

export default EmailCategoriesFilter;

export const selectionFilter = items => filter(items, isCategory);
