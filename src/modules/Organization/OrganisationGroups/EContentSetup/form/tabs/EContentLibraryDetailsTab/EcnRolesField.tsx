import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { isEmpty, map } from 'lodash';
import { DragDropContext, Droppable } from 'react-beautiful-dnd';
import classNames from 'classnames';
import uuidv4 from 'uuid/v4';

import EcnRoleItem from './EcnRoleItem';
import { IEntityFieldBasicProps } from '../../../../../../../common/components/containers/EntityForm/internal/EntityField';
import Statuses, { Active } from '../../../../../../../model/Statuses';
import useEntityFormContext from '../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import styles from './EcnRoleItem.scss';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import { IEcnRoleItem } from '../../../../../../../common/abstract/EContent/IEContentLibrary';
import AnimatedTitle from '../../../../../../../common/components/controls/base/AnimatedTitle';
import { useEditSession } from '../../../../../../../common/components/containers/EditSessionProvider';
import FilterBar from '../../../../../../../common/components/controls/FilterBar';

export type TStatus = { name: string; value: string };

export interface IEcnRolesField
  extends Partial<IEntityFieldBasicProps<string | unknown>> {
  isNew?: boolean;
  defaultValue?: string;
  hasOnlyActiveStatus?: boolean;
  hasOmitDeleteStatus?: boolean;
  label?: string;
  options?: Array<TStatus>;
}

const EcnRolesField: React.FC<IEcnRolesField> = () => {
  const t = useT();
  const { values, setFieldValue } = useEntityFormContext();
  const { isEditSessionActive } = useEditSession();

  const tempItem = {
    id: uuidv4(),
    name: '',
    status: 'ACTIVE',
    sequence: 1,
  };

  const ecnRoles = useMemo(
    () =>
      !values.ecnRoles || isEmpty(values.ecnRoles)
        ? [{ ...tempItem, id: uuidv4() }]
        : map(values.ecnRoles, x => ({
            ...tempItem,
            ...x,
          })),
    [values.ecnRoles, tempItem],
  );

  useEffect(() => {
    if (!isEmpty(ecnRoles[ecnRoles.length - 1].name)) {
      setFieldValue('ecnRoles', [
        ...ecnRoles,
        {
          ...tempItem,
          id: uuidv4(),
          sequence: (ecnRoles[ecnRoles.length - 1].sequence ?? 1) + 1,
        },
      ]);
    }
  }, [ecnRoles, setFieldValue, tempItem]);

  const [filter, setFilter] = useState({ status: Statuses.Active.value });
  const [filterChanged, setFilterChanged] = useState(false);

  const filteredEcnRoles = useMemo(() => {
    if (!filterChanged) {
      // Show all items when filter hasn't been explicitly changed
      return ecnRoles;
    }
    // Apply filter only when user has explicitly changed it
    return ecnRoles.filter(
      x => x.status === filter.status || x.status === null || x.status === '',
    );
  }, [ecnRoles, filter.status, filterChanged]);

  const handleFilterChange = useCallback(newFilter => {
    setFilter(newFilter);
    setFilterChanged(true);
  }, []);

  const renderItem = useCallback(
    (item: IEcnRoleItem) => {
      if (!isEditSessionActive && !item.name) return;
      const index = ecnRoles.findIndex(x => x.id === item.id);
      const showError =
        ecnRoles.filter(
          x =>
            !isEmpty(x.name) &&
            x.name?.toLowerCase() === item.name?.toLowerCase(),
        )?.length > 1;
      return (
        <EcnRoleItem
          key={index}
          isDraggable
          errorMessage={showError ? 'Role should unique' : null}
          index={index}
          item={item}
          lastItem={index > 0 && index === ecnRoles.length - 1}
        />
      );
    },
    [ecnRoles, isEditSessionActive],
  );

  function moveItemAndReorder(array, fromIndex, toIndex) {
    const newArray = [...array];
    const [movedItem] = newArray.splice(fromIndex, 1);
    newArray.splice(toIndex, 0, movedItem);
    return newArray;
  }

  const handleDragEnd = useCallback(
    result => {
      const { source, destination } = result;
      const ordered = moveItemAndReorder(
        ecnRoles,
        source.index,
        destination.index,
      );
      const newArr = ordered.map((x, index) => ({
        ...x,
        sequence: index + 1,
      }));
      setFieldValue('ecnRoles', newArr);
    },
    [ecnRoles, setFieldValue],
  );

  return (
    <>
      <div className="ml-20 mb-20">
        <FilterBar
          searchName="searchQuery"
          values={filter}
          onChange={handleFilterChange}
        >
          <FilterBar.StatusSelector hasMultiStatus={false} name="status" />
        </FilterBar>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="droppable-links">
          {provided => (
            <div ref={provided.innerRef} {...provided.droppableProps}>
              <div className={classNames(styles.processManagementWrapper)}>
                <div className="col-lg-3 col-md-6 col-sm-12 col-xs-12">
                  <AnimatedTitle
                    isRequired
                    className={classNames(styles.animatedTitle, 'mb-0')}
                    placeholder={t('Role')}
                  />
                </div>
                <div className="col-lg-3 col-md-6 col-sm-12 col-xs-12">
                  <AnimatedTitle
                    isRequired
                    className={classNames(styles.animatedTitle, 'mb-0 ml-5')}
                    placeholder={t('Status')}
                  />
                </div>
              </div>
              {map(filteredEcnRoles, renderItem)}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </>
  );
};

export default EcnRolesField;
