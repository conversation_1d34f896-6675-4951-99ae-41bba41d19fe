import { reduce } from 'lodash';

import { IEContentResourceXAttribute } from '../abstract/EContent/IEContentResourceXAttribute';
import attributeTypes from '../../model/EContentResourceXAttributeTypes';

const cookEContentResourceXAttributes = (
  attributes: IEContentResourceXAttribute[] = [],
): Record<string, IEContentResourceXAttribute> =>
  reduce(
    attributes,
    (acc, attribute) => {
      const attributeType =
        attributeTypes.BasicById[attribute?.attributeId]?.name;

      attributeType && (acc[attributeType] = attribute);
      return acc;
    },
    {},
  );

export const cookEContentResourceXAttributesCopied = (
  attributes: IEContentResourceXAttribute[] = [],
): Record<string, IEContentResourceXAttribute> =>
  reduce(
    attributes,
    (acc, attribute) => {
      const attributeType =
        attributeTypes.BasicById[attribute?.attributeId]?.name;

      attributeType &&
        (acc[attributeType] = {
          rule: attribute.rule,
          isChecked: attribute.isChecked,
          value: attribute.value,
          categories: attribute.categories,
        });
      return acc;
    },
    {},
  );

export default cookEContentResourceXAttributes;
