/* global swal */
import classnames from 'classnames';
import {
  differenceWith,
  find,
  findIndex,
  forEach,
  isFunction,
  last,
  some,
  remove,
  isEmpty,
  concat,
  groupBy as _groupBy,
  round,
} from 'lodash';
import flow from 'lodash/fp/flow';
import groupBy from 'lodash/fp/groupBy';
import map from 'lodash/fp/map';
import toPairs from 'lodash/fp/toPairs';
import PropTypes from 'prop-types';
import React from 'react';
import { withRouter } from 'react-router-dom';
import uuidv4 from 'uuid/v4';
import getBlobDuration from 'get-blob-duration';
import imageCompression from 'browser-image-compression';

import withTranslations from '../../../../common/components/utils/Translations/withTranslations';
import EplusPropTypes from '../../../../common/propTypes';
import withFsClient from '../../../data/upload/withFsClient';
import { ErrorWithHumanMessage } from '../../../errors';
import { toKb } from '../../../fileSizeHelper';
import nextAvailableFileName from '../../../nextAvailableFileName';
import FormControlErrorMessage from '../../utils/FormControlErrorMessage';
import UndoRemoveButton from '../UndoRemoveButton';
import UploadFileDialog from '../UploadFileDialog';
import AddAttachment from './addComponents/AddAttachment';
import styles from './Attachments.scss';
import AttachmentList from './lists/AttachmentList';
import swal2, { Swal } from '../../../utils/swal2';

const MILLISECONDS_IN_SECONDS = 1000;
const ROUND_TO_ONE = 1;
const ROUND_TO = 3;
const BYTES_IN_MEGABYTE = 1048576;
const BYTES_IN_KILOBYTE = 1024;

@withFsClient
@withRouter
@withTranslations
export default class Attachments extends React.PureComponent {
  static propTypes = {
    editable: PropTypes.bool.isRequired,
    hideAdd: PropTypes.bool,
    onUpdate: PropTypes.func.isRequired,
    categoryKey: PropTypes.string.isRequired,
    attachments: PropTypes.arrayOf(EplusPropTypes.fileAttachment).isRequired,
    onOpen: PropTypes.oneOfType([PropTypes.func, PropTypes.any]),
    onClose: PropTypes.oneOfType([PropTypes.func, PropTypes.any]),
    allowMultipleUpload: PropTypes.bool,
    isInPage: PropTypes.bool,
    addAttachmentComponent: PropTypes.func,
    attachmentListComponent: PropTypes.func,
    attachmentItemComponent: PropTypes.func,
    undoRemoveButtonComponent: PropTypes.func,
    contentWrapper: PropTypes.func,
    required: PropTypes.bool,
    isDownloadable: PropTypes.bool,
    allowedFileTypes: PropTypes.oneOfType([
      PropTypes.oneOf(EplusPropTypes.allowedFileTypes),
      PropTypes.arrayOf(PropTypes.oneOf(EplusPropTypes.allowedFileTypes)),
    ]),
    allowedNumberOfFiles: PropTypes.number,
    minimumNumberOfFiles: PropTypes.number,
    hasPhotoRecorder: PropTypes.bool,
    hasVideoRecorder: PropTypes.bool,
    hasAudioRecorder: PropTypes.bool,
    hasRoutes: PropTypes.bool,
    isListHidden: PropTypes.bool,
    hasUndo: PropTypes.bool,
    addButtonPosition: PropTypes.oneOf(['TOP', 'BOTTOM']),
    strikeTitle: PropTypes.string,
    addButtonTitle: PropTypes.string,
    onCancelButtonText: PropTypes.string,
    onCancelButtonStyle: PropTypes.oneOf(['link', 'bordered']),
    name: PropTypes.string,
    dropdownOptions: PropTypes.oneOfType([PropTypes.array, PropTypes.func]),
    removeOptions: PropTypes.func,
    isOverrideDropdownOptions: PropTypes.bool,
    errorMessage: PropTypes.string,
    organisationGroupId: PropTypes.number,
    tenantId: PropTypes.number,
    noWrapperStyle: PropTypes.bool,
    isAddCentered: PropTypes.bool,
    isSubsection: PropTypes.bool,
    isDisabled: PropTypes.bool,
    maxDescriptionLength: PropTypes.number,
    maxSize: PropTypes.number,
    maxDuration: PropTypes.number,
    hasErrorMessage: PropTypes.bool,
    listClassName: PropTypes.string,
    isTextToAudioEnabled: PropTypes.bool,
    audioValidation: PropTypes.bool,
    audioValidationParameters: {
      pitch: PropTypes.number,
      frequency: PropTypes.number,
      noise: PropTypes.number,
      loudness: PropTypes.number,
    },
    ...EplusPropTypes.translatePropTypes,
    ...withFsClient.props,
  };

  static defaultProps = {
    required: false,
    hideAdd: false,
    addAttachmentComponent: null,
    attachmentListComponent: null,
    undoRemoveButtonComponent: null,
    contentWrapper: null,
    allowMultipleUpload: true,
    isDownloadable: false,
    allowedFileTypes: '',
    allowedNumberOfFiles: 0,
    minimumNumberOfFiles: 0,
    hasPhotoRecorder: true,
    hasVideoRecorder: false,
    hasAudioRecorder: false,
    hasRoutes: false,
    isListHidden: false,
    hasUndo: true,
    addButtonPosition: 'TOP',
    strikeTitle: '',
    addButtonTitle: '',
    onCancelButtonText: 'Cancel',
    onCancelButtonStyle: 'link',
    onOpen: null,
    onClose: null,
    name: 'file-plus',
    dropdownOptions: [],
    removeOptions: null,
    isOverrideDropdownOptions: false,
    errorMessage: null,
    noWrapperStyle: false,
    isAddCentered: false,
    tenantId: null,
    organisationGroupId: null,
    isSubsection: false,
    maxDescriptionLength: null,
    isDisabled: false,
    isInPage: false,
    hasErrorMessage: true,
    listClassName: '',
    audioValidation: false,
  };

  constructor(props) {
    super(props);
    this.removed = React.createRef();
    this.removed.current = [];
    this.added = React.createRef();
    this.added.current = [];
  }

  state = {
    modalVisible: false,
    uploading: false,
  };

  handleModalOpen = e => {
    const { onOpen } = this.props;
    e && e.preventDefault();
    onOpen && onOpen();
    this.setState({ modalVisible: true });
  };

  handleModalClose = () => {
    const { onClose } = this.props;
    onClose && onClose();
    this.setState({ modalVisible: false });
  };

  handleOnCancel = () => {
    const { fsClient, onUpdate, attachments } = this.props;
    const added = this.added.current;

    for (const file of added) {
      try {
        const { uploadToken } = file;
        fsClient.cancelUpload(uploadToken);
      } catch (e) {
        console.warn(e);
      }
    }

    const filtered = this.added.current.filter(x => x.type !== 'SPLITTED');
    const result = this.getDiff(attachments, filtered);
    onUpdate(result);

    this.added.current = [];
    this.removed.current = [];
    this.handleModalClose();
  };

  handleRemoveFile = file => {
    const { onUpdate, attachments } = this.props;

    this.removed.current = [...this.removed.current, file];
    this.added.current = this.getDiff(this.added.current, [file]);
    const result = this.getDiff(attachments, this.removed.current);
    onUpdate(result);
  };

  base64ToFile = (base64String, fileName) => {
    const byteString = atob(base64String.split(',')[1]); // Remove the data URL part (if present)
    const byteArray = new Uint8Array(byteString.length);
    for (let i = 0; i < byteString.length; i++) {
      byteArray[i] = byteString.charCodeAt(i);
    }
    return new File([byteArray], fileName, {
      type: base64String.split(',')[0].split(':')[1].split(';')[0],
    });
  };

  handleValidateSplittedFiles = splittedFiles => {
    const { attachments, allowedNumberOfFiles, t } = this.props;
    const added = this.added.current;

    const totalFiles =
      attachments.length + splittedFiles.length + added.length - 1;

    if (allowedNumberOfFiles && totalFiles > allowedNumberOfFiles) {
      const fileTitle = allowedNumberOfFiles > 1 ? 'files' : 'file';
      swal({
        text: t(`No more than #{allowedNumberOfFiles} #{fileTitle} allowed`, {
          allowedNumberOfFiles,
          fileTitle,
        }),
        icon: 'info',
        dangerMode: true,
      });
      return true;
    }
  };

  handleSplitAudio = async (attachment, splittedFiles) => {
    const invalid = this.handleValidateSplittedFiles(splittedFiles);
    if (invalid) return;
    for (const item of splittedFiles) {
      const file = this.base64ToFile(item, `${uuidv4()}.mp3`);
      const fileURL = URL.createObjectURL(file);
      const duration = (await getBlobDuration(file)) * MILLISECONDS_IN_SECONDS;
      await this.handleOnNewUploadRequest(
        {
          fileName: file.name,
          progress: 0,
          mimeType: file.type || 'application/octet-stream',
          fileSize: file.size,
          description: '',
          data: file,
          preview: fileURL,
          duration,
          type: 'SPLITTED',
        },
        true,
      );
    }
  };

  handleResizeAndUpload = async (file, fileName) => {
    const { maxSize } = this.props;

    if (!maxSize) return;
    const compressFile = async inputFile => {
      const compressedFile = await imageCompression(inputFile, {
        maxSizeMB: maxSize / BYTES_IN_MEGABYTE,
        useWebWorker: true,
      });
      if (compressedFile.size > maxSize) {
        return await compressFile(compressedFile);
      }
      return compressedFile;
    };

    const finalCompressedFile = await compressFile(file);

    return new File([finalCompressedFile], fileName, {
      type: 'image/png',
    });
  };

  checkFileSize = async (file, fileName) => {
    const { maxSize, t } = this.props;
    if (maxSize && file.size > maxSize) {
      const result = await swal2({
        text: t(
          `Photo exceeds the maximum allowed size of ${
            round(maxSize / BYTES_IN_MEGABYTE, ROUND_TO) >= 1
              ? `${round(maxSize / BYTES_IN_MEGABYTE, ROUND_TO_ONE)} MB`
              : `${round(maxSize / BYTES_IN_KILOBYTE)} KB`
          }`,
        ),
        icon: 'info',
        className: classnames(styles.modalWrapper),
        confirmButtonText: t('Resize'),
        cancelButtonText: t('Cancel'),
        preConfirm: async () => {
          const confirmButton = Swal.getConfirmButton();
          if (confirmButton) {
            confirmButton.innerHTML = '<i class="icon-spinner4 spinner" />';
            confirmButton.disabled = true; // Disable the button while loading
          }

          const result = await this.handleResizeAndUpload(file, fileName);
          return result;
        },
      });
      if (result.isConfirmed && result.value) {
        return result.value;
      }
      return false;
    }
    return true;
  };

  handleImageAiImprovement = async (attachment, image) => {
    const file = await this.base64ToFile(image, attachment.fileName);
    const fileURL = URL.createObjectURL(file);

    const response = await this.checkFileSize(file, attachment.fileName);
    if (!response) return;

    this.handleRemoveFile(attachment);

    await this.handleOnNewUploadRequest(
      {
        fileName: attachment.fileName,
        progress: 0,
        mimeType: 'image/png',
        fileSize: response === true ? file.size : response.size,
        description: '',
        data: response === true ? file : response,
        preview: fileURL,
        type: 'IMAGE_AI_IMPROVEMENT',
        groupSequence: attachment.groupSequence,
      },
      true,
    );
  };

  handleUndoRemoveFile = () => {
    const { onUpdate, attachments } = this.props;
    onUpdate(this.mergeAttachmentsByName(attachments, this.removed.current));
    this.removed.current = [];
  };

  handleUploadFiles = () => {
    const { onUpdate, attachments } = this.props;
    onUpdate(this.mergeAttachmentsByName(attachments, this.added.current));
    this.added.current = [];
    this.handleModalClose();
  };

  updateFile = (filePatch, fileMatcher, callback) => {
    const filePatches = Array.isArray(filePatch) ? filePatch : [filePatch];
    const prevAdded = this.added.current;
    const prevRemoved = this.removed.current;

    const added = [...prevAdded];

    const fileIndex = findIndex(added, fileMatcher);
    const fileIndexRemoved = findIndex(prevRemoved, fileMatcher);

    /**
     * This checks for inconsistent state where added file's upload is in progress
     */
    if (added[fileIndex] && prevRemoved[fileIndexRemoved]) {
      return false;
    }

    if (added[fileIndex]) {
      forEach(filePatches, patch => Object.assign(added[fileIndex], patch));
    }

    let removed = [...prevRemoved];

    forEach(filePatches, (acc, patch) => {
      if (isEmpty(removed)) {
        return false;
      }

      removed = remove(removed, { fileName: patch.fileName });
    });

    this.added.current = added;
    this.removed.current = removed;
    callback && callback(added);
  };

  updateAdded = values => {
    const { attachments, onUpdate } = this.props;
    const valuesWithKeyword = values.map(item => {
      const match = attachments.find(x => x.fileName === item.fileName);
      return match ? { ...item, contentKeyword: match.contentKeyword } : item;
    });
    const newlyAdded = this.mergeAttachmentsByName(
      attachments,
      valuesWithKeyword,
    );
    const result = this.getDiff(newlyAdded, this.removed.current);
    onUpdate(result);
  };

  updateUploading = value => {
    this.setState({ uploading: value });
  };

  handleOnNewUploadRequest = async (file, noCheck = false) => {
    const {
      t,
      categoryKey,
      attachments,
      fsClient,
      onUpdate,
      organisationGroupId,
      tenantId,
    } = this.props;
    const { modalVisible } = this.state;
    if (!noCheck) {
      if (!modalVisible) return;
    }
    const added = this.added.current;
    const { fileName } = file;
    const names = [...attachments, ...added].map(x => x.fileName);
    const existsInAttachments = find(attachments, { fileName });
    const existsInAdded = find(added, { fileName });
    this.setState({ uploading: true });
    if (existsInAdded && !existsInAttachments) {
      if (existsInAdded.fileSize !== file.fileSize) {
        file.fileName = nextAvailableFileName(fileName, names);
      } else {
        return;
      }
    } else if (existsInAttachments) {
      const replace = await swal({
        text: t('This attachment already exists, replace or add the new file'),
        icon: 'info',
        buttons: [t('New file'), t('Replace')],
        dangerMode: true,
      });

      if (replace) {
        file.fileId = existsInAttachments.fileId;
      } else {
        file.fileName = nextAvailableFileName(fileName, names);
      }
    }

    const temporaryUploadToken = uuidv4();

    file.uploadToken = temporaryUploadToken;
    file.uploading = true;

    this.added.current = [
      ...this.added.current,
      {
        ...file,
        ...(file.type === 'IMAGE_AI_IMPROVEMENT'
          ? { description: 'Modified' }
          : {}),
      },
    ];

    try {
      const config = await fsClient.getUploadUrl(file, categoryKey, {
        organisationGroupId,
        tenantId,
      });

      if (!config.ok) {
        throw new ErrorWithHumanMessage(
          t(config.error, {
            maxFileSize: toKb(config.maxFileSize),
            canUploadSize: toKb(config.canUploadSize),
          }),
        );
      }
      file.uploadToken = config.uploadToken;

      this.updateFile(
        [
          { uploadToken: config.uploadToken },
          { category: categoryKey },
          { url: file.preview },
        ],
        { uploadToken: temporaryUploadToken },
        added => {
          this.updateAdded(added);
        },
      );
      this.setState({ uploading: false });

      await fsClient.uploadFile(file, config, progress => {
        this.updateFile(
          { progress },
          {
            uploadToken: config.uploadToken,
          },
          added => {
            this.updateAdded(added);
          },
        );
      });
    } catch (e) {
      this.setState({ uploading: false });
      this.updateFile(
        { error: e },
        { uploadToken: temporaryUploadToken },
        added => {
          this.updateAdded(added);
        },
      );
    }

    this.updateFile(
      { uploading: false },
      {
        uploadToken: file.uploadToken,
      },
      added => {
        this.updateAdded(added);
      },
    );
  };

  handleOnCancelUploadRequest = (uploadToken, fileId) => {
    const { fsClient, attachments } = this.props;
    const file = attachments.find(file =>
      uploadToken ? file.uploadToken === uploadToken : file.fileId === fileId,
    );
    this.handleRemoveFile(file);
    /*
     * @TODO by default fs-client dont support removing files from the already
     * uploaded storage this needs to be improved
     */
    uploadToken &&
      fsClient.cancelUpload(uploadToken).catch(e => {
        console.warn(e);
      });
  };

  getDiff = (array1, array2) =>
    differenceWith(
      array1,
      array2,
      (a, b) => a.fileId === b.fileId && a.uploadToken === b.uploadToken,
    );

  mergeAttachmentsByName = (array1, array2) =>
    flow(
      groupBy('fileName'),
      toPairs,
      map(x => last(x[1])),
    )([...array1, ...array2]);

  hasRemoved = () => some(this.removed.current);

  handleValidate = input => {
    const {
      attachments,
      allowedNumberOfFiles,
      minimumNumberOfFiles,
      t,
    } = this.props;
    const added = this.added.current;

    const totalFiles = attachments.length + input.length + added.length;

    const fileInput = input.some(item => 'webkitRelativePath' in item);

    if (minimumNumberOfFiles && totalFiles < minimumNumberOfFiles) {
      swal({
        text: t(
          `No less than #{minimumNumberOfFiles} files allowed`,
          minimumNumberOfFiles,
        ),
        icon: 'info',
        dangerMode: true,
      });
      return true;
    }

    const filteredInput = input.filter(
      x =>
        !(
          x.preview?.startsWith('https://ed-admin-uploads') ||
          x.preview?.startsWith('https://client')
        ),
    );

    const combined = concat(attachments, filteredInput);

    const withPreview = combined.filter(item => 'preview' in item);
    const withoutPreview = combined.filter(item => !('preview' in item));

    const grouped = _groupBy(withPreview, 'preview');

    const mergedWithToken = Object.values(grouped).map(group =>
      Object.assign({}, ...group),
    );

    const merged = [...mergedWithToken, ...withoutPreview];
    const withUploadToken = merged.some(item => 'uploadToken' in item);
    const totalAdded = merged.length;

    if (
      allowedNumberOfFiles &&
      (!fileInput
        ? totalAdded >= allowedNumberOfFiles
        : totalAdded > allowedNumberOfFiles)
    ) {
      const fileTitle = allowedNumberOfFiles > 1 ? 'files' : 'file';
      swal({
        text: t(`No more than #{allowedNumberOfFiles} #{fileTitle} allowed`, {
          allowedNumberOfFiles,
          fileTitle,
        }),
        icon: 'info',
        dangerMode: true,
      });
      return true;
    }
    return null;
  };

  get content() {
    const {
      editable,
      attachments,
      required,
      attachmentListComponent,
      undoRemoveButtonComponent,
      contentWrapper: ContentWrapper,
      isDownloadable,
      isListHidden,
      addButtonPosition,
      hasUndo,
      errorMessage,
      attachmentItemComponent,
      maxDescriptionLength,
      isDisabled,
      hasErrorMessage,
      listClassName,
      strikeTitle,
      allowedNumberOfFiles,
      isThreadAttachment,
      hasLowStrike,
    } = this.props;

    const isAddComponentVisible =
      editable || (!editable && !isEmpty(attachments));

    const ListComponent = attachmentListComponent || AttachmentList;

    const UndoRemoveButtonComponent =
      undoRemoveButtonComponent || UndoRemoveButton;

    const content = (
      <>
        {isAddComponentVisible &&
          addButtonPosition === 'TOP' &&
          this.renderAddComponent()}
        {isListHidden && hasUndo && editable && this.hasRemoved() && (
          <UndoRemoveButtonComponent onUndoRemove={this.handleUndoRemoveFile} />
        )}
        {!isListHidden && (
          <ListComponent
            allowedNumberOfFiles={allowedNumberOfFiles}
            attachmentItemComponent={attachmentItemComponent}
            attachments={attachments}
            dropdownOptions={this.getDropdownOptions}
            editable={editable && !isDisabled}
            hasLowStrike={hasLowStrike}
            isDownloadable={isDownloadable}
            isThreadAttachment={isThreadAttachment}
            listClassName={listClassName}
            maxDescriptionLength={maxDescriptionLength}
            onChange={this.handleChangeAttachment}
            onImageAiImprovement={this.handleImageAiImprovement}
            onRemove={this.handleRemoveFile}
            onSplitAudio={this.handleSplitAudio}
          >
            {hasUndo && editable && this.hasRemoved() && (
              <UndoRemoveButtonComponent
                onUndoRemove={this.handleUndoRemoveFile}
              />
            )}
          </ListComponent>
        )}
        {isAddComponentVisible &&
          addButtonPosition === 'BOTTOM' &&
          this.renderAddComponent()}
        {required && strikeTitle === '' && (
          <span className="text-danger">&nbsp;*</span>
        )}
        {hasErrorMessage && (
          <FormControlErrorMessage isStatic errorMessage={errorMessage} />
        )}
      </>
    );

    const wrappedContent = ContentWrapper && (
      <ContentWrapper>{content}</ContentWrapper>
    );

    return wrappedContent || content;
  }

  getDropdownOptions = data => {
    const {
      t,
      dropdownOptions,
      isOverrideDropdownOptions,
      removeOptions,
    } = this.props;
    const _dropdownOptions = isFunction(dropdownOptions)
      ? dropdownOptions(data)
      : dropdownOptions;
    const hasRemoveOption = removeOptions ? removeOptions(data) : true;

    return isOverrideDropdownOptions
      ? _dropdownOptions
      : [
          ..._dropdownOptions,
          hasRemoveOption && {
            mimeType: 'all',
            label: t('Remove'),
            icon: 'ed-remove',
            iconType: 'ed',
            onClick: this.handleRemoveFile,
          },
        ];
  };

  handleChangeAttachment = attachment => {
    const { onUpdate, attachments } = this.props;

    onUpdate(
      (attachments || []).map(a =>
        a.fileId === attachment.fileId &&
        a.uploadToken === attachment.uploadToken
          ? Object.assign({}, attachment, {
              description: attachment.description,
            })
          : a,
      ),
    );
  };

  renderAddComponent = () => {
    const {
      addAttachmentComponent,
      allowedNumberOfFiles,
      isInPage,
      attachments,
      editable,
      strikeTitle,
      addButtonTitle,
      name,
      isAddCentered,
      isSubsection,
      isDisabled,
      hideAdd,
      required,
      label,
    } = this.props;
    const { modalVisible } = this.state;
    const AddComponent = addAttachmentComponent || AddAttachment;

    return (
      !hideAdd && (
        <AddComponent
          isLeft
          addButtonTitle={addButtonTitle}
          allowedNumberOfFiles={allowedNumberOfFiles}
          attachments={attachments}
          editable={editable}
          isCentered={isAddCentered}
          isDisabled={isDisabled}
          isInPage={isInPage}
          isSubsection={isSubsection}
          isVisible={modalVisible}
          name={name}
          required={required}
          strikeTitle={strikeTitle}
          onDropFile={this.handleOnNewUploadRequest}
          onModalOpen={this.handleModalOpen}
          onRemoveFiles={this.handleRemoveFile}
        />
      )
    );
  };

  render() {
    const {
      attachments,
      isInPage,
      allowMultipleUpload,
      allowedFileTypes,
      hasPhotoRecorder,
      hasVideoRecorder,
      hasAudioRecorder,
      hasRoutes,
      onCancelButtonText,
      onCancelButtonStyle,
      noWrapperStyle,
      maxSize,
      maxDuration,
      isTextToAudioEnabled,
      audioValidation,
      audioValidationParameters,
    } = this.props;
    const { modalVisible, removed, uploading } = this.state;

    const files = this.getDiff(attachments, removed);

    return (
      <div className={classnames({ [styles.wrapper]: !noWrapperStyle })}>
        {this.content}
        <UploadFileDialog
          allowedFileTypes={allowedFileTypes}
          audioValidation={audioValidation}
          audioValidationParameters={audioValidationParameters}
          files={files}
          hasAudioRecorder={hasAudioRecorder}
          hasPhotoRecorder={hasPhotoRecorder}
          hasRoutes={hasRoutes}
          hasVideoRecorder={hasVideoRecorder}
          isInPage={isInPage}
          isMultiple={allowMultipleUpload}
          isTextToAudioEnabled={isTextToAudioEnabled}
          isUploading={uploading}
          maxDuration={maxDuration}
          maxSize={maxSize}
          updateUploading={this.updateUploading}
          visible={modalVisible}
          onCancel={this.handleOnCancel}
          onCancelButtonStyle={onCancelButtonStyle}
          onCancelButtonText={onCancelButtonText}
          onCancelUploadRequest={this.handleOnCancelUploadRequest}
          onConfirm={this.handleUploadFiles}
          onConfirmButtonText="Attach"
          onNewUploadRequest={this.handleOnNewUploadRequest}
          onValidate={this.handleValidate}
        />
      </div>
    );
  }
}
