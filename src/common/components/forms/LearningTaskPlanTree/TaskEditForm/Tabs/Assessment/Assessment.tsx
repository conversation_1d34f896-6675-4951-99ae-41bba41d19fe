/* global swal */
import {
  startsWith,
  map,
  sum,
  filter,
  groupBy,
  max,
  some,
  set,
  orderBy,
  includes,
  isEmpty,
} from 'lodash';
import React, { useCallback, useMemo, useState } from 'react';

import Rubric from './Rubric/Rubric';
import DataAccessComponent from '../../../../../../data/support/DataAccessComponent';
import {
  Dropdown,
  Emoticon,
  Mark,
  OptionsSet,
  Text,
  Tick,
} from '../../../../../../../model/AssessmentTypes';
import { OneDecimal } from '../../../../../../../model/GradeScaleRoundingStatus';
import TaskTickStyle from '../../../../../../../model/TaskTickStyle';
import EntityForm from '../../../../../../components/containers/EntityForm';
import EntityFormFieldSet from '../../../../../../components/containers/EntityForm/EntityFormFieldSet';
import subjectOptionSetsGql from '../../../../../../data/ProgramGroups/subjectOptionSets.graphql';
import Notifications from '../../../../../../utils/Notifications';
import DependsOnFields from '../../../../../containers/EntityForm/DependsOnFields';
import CharactersField from '../../../../../containers/EntityForm/fields/CharactersField';
import CheckboxField from '../../../../../containers/EntityForm/fields/CheckboxField';
import EntityNameField from '../../../../../containers/EntityForm/fields/EntityNameField';
import NumberField from '../../../../../containers/EntityForm/fields/NumberField';
import RoundingField from '../../../../../containers/EntityForm/fields/RoundingField';
import SelectBoxField from '../../../../../containers/EntityForm/fields/SelectBoxField';
import { RETURN_TYPE } from '../../../../../controls/base/SelectBox';
import useT from '../../../../../utils/Translations/useT';
import AssessmentOptionsField from '../../../AssessmentOptionsField';
import AssessmentTypeField from '../../../AssessmentTypeField';
import EmoticonSelectorField from '../../../EmoticonSelectorField';
import CalculationArrayField from '../../CalculationArrayField';
import { TTaskEditFormEntity } from '../../TaskEditForm';
import Icon from '../../../../../utils/Icon';
import { isLearningSpaceLPTask } from '../../../../../../../model/LearningPlanTreeTypes';
import StatusWithDraft from '../../../../../../../model/StatusWithDraft';
import {
  Percent,
  PercentRange,
  PointsRange,
} from '../../../../../../../model/RubricCalculationMethods';
import { MAX_PERCENT } from './Rubric/const';
import {
  isRubricMatrix,
  resolveNodeId,
} from './Rubric/RubricTable/RubricTable';
import { INFINITY } from '../../../../../../const';

export const CHARACTERS_MIN = 1;
export const CHARACTERS_MAX = 20;
export const RANGES = [
  { min: 10, max: 100, step: 10 },
  { min: 100, max: 500, step: 50 },
  { min: 500, max: 1000, step: 100 },
  { min: 1000, max: 10000, step: 500 },
  { min: 10000, max: 21000, step: 1000 },
];

export interface IAssessment {
  onCancel?: () => void;
  entity: TTaskEditFormEntity;
  onSubmit: (values: TTaskEditFormEntity) => Promise<void>;
  isReadOnly?: boolean;
  title?: string;
  updateMessageText?: string;
  programGroupId?: number;
  programGroupTypeId: number;
  parentUrl: string;
  hasCopyRubricButton?: boolean;
  intakeId?: number;
}

const Assessment: React.FC<Readonly<IAssessment>> = ({
  entity,
  onSubmit,
  isReadOnly = false,
  title,
  updateMessageText = 'Updated Successfully',
  onCancel,
  programGroupId,
  programGroupTypeId,
  parentUrl,
  hasCopyRubricButton = false,
  intakeId,
}) => {
  const t = useT();
  const [isRubricChanged, setIsRubricChanged] = useState(false);

  const onRubricToggle = useCallback(val => {
    setIsRubricChanged(val);
  }, []);

  const handleSubmit = useCallback(
    async ({ assessedCount, ...values }) => {
      if (onSubmit) {
        let isAllowed = true;
        if (
          isLearningSpaceLPTask(entity) &&
          entity.assessmentType !== values.assessmentType &&
          assessedCount
        ) {
          isAllowed = await swal({
            text: t(
              `Are you sure you want to perform action? The system should remove all marks with previous assessment type.`,
            ),
            icon: 'warning',
            buttons: [t('Cancel'), t('Delete')],
            dangerMode: true,
          });
        }

        if (!isAllowed) {
          return false;
        }

        return onSubmit(values);
      }
    },
    [entity, t, onSubmit],
  );

  const saveData = useCallback(
    async (values, { isDirty, location, isValid }) => {
      const { rubricTreeState, rubric } = values;
      const percentsSumError = validatePercentsSum(
        rubricTreeState,
        rubric?.calculationMethod,
      );

      if (
        !isReadOnly &&
        isDirty &&
        isValid &&
        !percentsSumError &&
        startsWith(location.pathname, parentUrl) &&
        location.pathname !== parentUrl
      ) {
        await onSubmit(values);
      }
    },
    [parentUrl, isReadOnly, onSubmit],
  );

  const handleRenderOption = useCallback(item => {
    if (!item?.title) {
      return <span>Tick Style *</span>;
    }

    return <Icon hoverTitle={item?.title} name={item?.title} />;
  }, []);

  const handleRenderSelected = useCallback(item => {
    if (!item?.icon) {
      return <span className="pull-left filter-option">Tick Style *</span>;
    }

    return <Icon hoverTitle={item?.title} name={item?.icon} />;
  }, []);

  const handleOptionTitle = useCallback(item => item?.icon, []);

  const isUsedInAssessment = useMemo(
    () => entity?.rubric?.isUsedInStudentsAssessment,
    [entity],
  );

  const validateForm = useCallback(
    values => {
      const { rubricTreeState, hasRubric, rubric } = values;

      const errors = {};

      if (!rubricTreeState) {
        return errors;
      }

      if (!hasRubric) {
        return errors;
      }

      const { models = [] } = rubricTreeState;

      if (isEmptyRubric(models)) {
        return errors;
      }

      if (
        includes(
          [PointsRange.value, PercentRange.value],
          rubric?.calculationMethod,
        )
      ) {
        const matrices = filter(models, isRubricMatrix);
        const matricesBySkillId = groupBy(matrices, 'lpTaskRubricSkillId');

        let error = false;

        map(matricesBySkillId, skillMatrices => {
          const orderedMatrices = orderBy(skillMatrices, [
            ({ pointsFrom }) => parseFloat(pointsFrom) || 0,
          ]);

          map(orderedMatrices, (matrix, index) => {
            const previousMatrixPointsFrom = parseFloat(
              orderedMatrices[index - 1]?.pointsFrom,
            );
            const previousMatrixPointsTo = parseFloat(
              orderedMatrices[index - 1]?.pointsTo,
            );
            if (
              index &&
              parseFloat(matrix?.pointsFrom) <= previousMatrixPointsTo
            ) {
              const key = resolveNodeId(matrix);
              error = true;
              set(
                errors,
                ['rubricTreeState', 'models', key, 'criteria'],
                `Overlaps with ${previousMatrixPointsFrom} - ${previousMatrixPointsTo}`,
              );
            }
          });
        });

        if (error) {
          Notifications.error('Error', 'The ranges should not overlap', t);
        }
      }

      if (
        !isEmptyRubric(models) &&
        hasRubric &&
        [PercentRange.value, Percent.value].includes(rubric?.calculationMethod)
      ) {
        const error = validatePercentsSum(
          rubricTreeState,
          rubric?.calculationMethod,
        );

        if (error) {
          set(errors, 'percentage', error);
          Notifications.error('Error', error, t);
        }
      }

      return errors;
    },
    [t],
  );

  return (
    <EntityForm
      createLabel="Update"
      entity={entity}
      entityName="LearningPlanTaskAssessment"
      hasValidateOnBlur={false}
      hasValidateOnChange={false}
      isCopied={isRubricChanged ? isRubricChanged : undefined}
      isReadOnly={isReadOnly}
      title={title}
      updateLabel={t('Update')}
      validate={validateForm}
      validateOnMount={false}
      onCancel={onCancel}
      onLeave={saveData}
      onSubmit={handleSubmit}
    >
      <EntityFormFieldSet>
        <CheckboxField label={t('Assessed')} name="assessed" />
      </EntityFormFieldSet>
      <DependsOnFields
        fields={{
          assessed: 'assessed',
          assessmentType: 'assessmentType',
        }}
      >
        {({ assessed, assessmentType }) =>
          assessed ? (
            <>
              <EntityFormFieldSet className="mb-20">
                <AssessmentTypeField
                  columns={3}
                  defaultValue={Mark.value}
                  isEmptyValueAllowed={false}
                  isReadOnly={!assessed}
                  label={t('Assessment type')}
                  required={!!assessed}
                />
                <EntityNameField
                  columns={3}
                  label={t('Assessment label')}
                  name="assessmentLabel"
                  required={false}
                />

                {assessmentType === Tick.value ? (
                  <SelectBoxField
                    required
                    columns={3}
                    label={t('Tick Style')}
                    name="tickStyle"
                    options={TaskTickStyle.Basic}
                    returnType={RETURN_TYPE.STRING}
                    onOptionContentRender={handleRenderOption}
                    onOptionTitle={handleOptionTitle}
                    onRenderSelected={handleRenderSelected}
                  />
                ) : null}

                {assessmentType === Text.value ? (
                  <CharactersField
                    isRequired
                    columns={3}
                    fromLabel={t('From')}
                    fromName="numCharsFrom"
                    max={CHARACTERS_MAX}
                    min={CHARACTERS_MIN}
                    ranges={RANGES}
                    step={1}
                    title={t('Number of characters')}
                    toLabel={t('To')}
                    toName="numCharsTo"
                  />
                ) : null}

                {assessmentType === Emoticon.value ? (
                  <EmoticonSelectorField
                    isRequired
                    itemValuePropName="id"
                    label={t('Emoticon')}
                    name="emoticons"
                  />
                ) : null}

                {assessmentType === OptionsSet.value ? (
                  <DataAccessComponent
                    query={subjectOptionSetsGql}
                    variables={{
                      count: INFINITY,
                      programGroupId,
                      status: optionSetsStatuses,
                    }}
                  >
                    {({ subjectOptionSets }) => (
                      <SelectBoxField
                        required
                        columns={3}
                        itemValuePropName="id"
                        label={t('Options set')}
                        name="subjectOptionSetId"
                        options={subjectOptionSets}
                        returnType={RETURN_TYPE.NUMBER}
                      />
                    )}
                  </DataAccessComponent>
                ) : null}

                {assessmentType === Mark.value ? (
                  <RoundingField
                    columns={3}
                    defaultValue={OneDecimal.value}
                    isEmptyValueAllowed={false}
                    isReadOnly={isReadOnly}
                    label={t('Rounding')}
                    required={!isReadOnly}
                    returnType="number"
                  />
                ) : null}
              </EntityFormFieldSet>

              <EntityFormFieldSet>
                {assessmentType === Dropdown.value ? (
                  <AssessmentOptionsField
                    columns={3}
                    label={t('Options')}
                    name="assessmentOptions"
                  />
                ) : null}
              </EntityFormFieldSet>

              {assessmentType === Mark.value ? (
                <>
                  <EntityFormFieldSet>
                    <NumberField
                      columns={3}
                      defaultValue={0}
                      isReadOnly={!assessed}
                      label={t('Maximum')}
                      min={0}
                      name="maximum"
                      required={!!assessed}
                    />
                    <NumberField
                      columns={3}
                      defaultValue={0}
                      isReadOnly={!assessed}
                      label={t('Weight')}
                      min={0}
                      name="weight"
                      required={!!assessed}
                    />
                  </EntityFormFieldSet>
                  <CalculationArrayField
                    isReadOnly={isReadOnly || !assessed}
                    programGroupId={programGroupId}
                    programGroupTypeId={programGroupTypeId}
                  />
                </>
              ) : null}
            </>
          ) : null
        }
      </DependsOnFields>
      <Rubric
        hasCopyRubricButton={hasCopyRubricButton}
        intakeId={intakeId}
        isAddDeleteDisabled={isUsedInAssessment}
        isReadOnly={isReadOnly}
        prevHasRubric={entity.hasRubric}
        programGroupTypeId={programGroupTypeId}
        onRubricToggle={onRubricToggle}
      />
    </EntityForm>
  );
};

export default Assessment;

const optionSetsStatuses = map(StatusWithDraft.Basic, 'value');

const isEmptyRubric = models =>
  !some(
    models,
    (model: {
      name?: string;
      criteria?: string;
      pointsFrom?: number;
      pointsTo?: number;
    }) => model?.name || model?.criteria || model?.pointsFrom || model.pointsTo,
  );

const validatePercentsSum = (rubricTreeState, calculationMethod) => {
  if (!rubricTreeState || !calculationMethod) {
    return '';
  }
  const { models } = rubricTreeState;
  const matrices = filter(models, isRubricMatrix);
  const matricesBySkillId = groupBy(matrices, 'lpTaskRubricSkillId');
  const pointsFrom = map(matricesBySkillId, arr =>
    max(map(arr, ({ pointsFrom }) => parseFloat(pointsFrom) || 0)),
  );

  const pointsTo = map(matricesBySkillId, arr =>
    max(map(arr, ({ pointsTo }) => parseFloat(pointsTo) || 0)),
  );
  const pointsFromSum = sum(pointsFrom);
  const pointsToSum = sum(pointsTo);
  if (
    (calculationMethod === Percent.value && pointsFromSum < MAX_PERCENT) ||
    (calculationMethod === PercentRange.value && pointsToSum < MAX_PERCENT)
  ) {
    return `The sum of the maximum percentages for all skills cannot be less than 100`;
  }

  if (
    (calculationMethod === Percent.value && pointsFromSum > MAX_PERCENT) ||
    (calculationMethod === PercentRange.value && pointsToSum > MAX_PERCENT)
  ) {
    return `The sum of the maximum percentages for all skills cannot be more than 100`;
  }

  return '';
};
