.stickyButton {
  position: sticky !important;
  bottom: 0px;
  right: 0px;
  float: right;
  z-index: 2;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

.fixedButton {
  position: fixed !important;
  bottom: 30px;
  right: 45px;
  float: right;
  z-index: 2;
}

.floatButtonForm {
  position: relative;
  max-height: 900px;
  overflow-y: auto;
}

.hiddenButton {
  opacity: 0 !important;
  visibility: hidden !important;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}