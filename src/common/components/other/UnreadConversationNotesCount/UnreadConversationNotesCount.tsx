import classNames from 'classnames';
import React, { FC } from 'react';
import styles from './UnreadConversationNotesCount.scss';

const UnreadConversationNotesCount: FC<IUnreadConversationNotesCountProps> = ({
  count,
}) => <span className={classNames(styles.count, 'count')}>{count}</span>;

export default UnreadConversationNotesCount;

interface IUnreadConversationNotesCountProps {
  count: number;
}
