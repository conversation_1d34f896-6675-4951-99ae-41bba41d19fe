import classNames from 'classnames';
import React, { FC } from 'react';
import styles from './UnreadConversationNotesCount.scss';
import useUnreadConversationNotesCount from '../../../data/hooks/useUnreadConversationNotesCount';

const UnreadConversationNotesCountHook: FC<IUnreadConversationNotesCountHookProps> = ({
  conversationId,
}) => {
  const unreadCount = useUnreadConversationNotesCount(conversationId) || 0;
  return unreadCount ? (
    <span className={classNames(styles.count, 'count')}>{unreadCount}</span>
  ) : null;
};

export default UnreadConversationNotesCountHook;

interface IUnreadConversationNotesCountHookProps {
  conversationId: number;
}
