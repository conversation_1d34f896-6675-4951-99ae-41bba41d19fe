import classNames from 'classnames';
import React, { FC } from 'react';
import styles from './UnreadConversationCount.scss';
import useUnreadConversationCount from '../../../data/hooks/useUnreadConversationCount';

const UnreadConversationCountHook: FC<IUnreadConversationCountHookProps> = ({
  conversationId,
}) => {
  const unreadCount = useUnreadConversationCount(conversationId) || 0;
  return unreadCount ? (
    <span className={classNames(styles.count, 'count')}>{unreadCount}</span>
  ) : null;
};

export default UnreadConversationCountHook;

interface IUnreadConversationCountHookProps {
  conversationId: number;
}
