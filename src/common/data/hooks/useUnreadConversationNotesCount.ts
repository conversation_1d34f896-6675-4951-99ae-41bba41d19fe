import { useQuery } from 'react-apollo';
import useSocketHandler from '../socket/useSocketHandler';
import conversation from '../../../modules/EdCom/Conversations/data/conversation.graphql';

export default function useUnreadConversationNotesCount(
  conversationId: number,
): number | undefined {
  const { data, refetch } = useQuery(conversation, {
    variables: { id: conversationId },
    skip: !conversationId,
  });

  useSocketHandler('conversation', refetch);

  return data?.conversation?.unreadConversationNotesCount || 0;
}
